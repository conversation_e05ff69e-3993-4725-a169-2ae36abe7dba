package com.jinghang.capital.core.banks.hxbk.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 灵活的Date反序列化器，支持多种日期格式
 * 可以同时兼容带T和不带T的日期时间格式
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class FlexibleDateDeserializer extends JsonDeserializer<Date> {
    
    // 支持的日期格式（按优先级排序）
    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd'T'HH:mm:ss",    // ISO 8601格式：2025-07-29T00:00:00
        "yyyy-MM-dd HH:mm:ss",      // 标准格式：2025-07-29 00:00:00
        "yyyy-MM-dd"                // 日期格式：2025-07-29
    };
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 尝试各种格式进行解析
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
                return sdf.parse(value);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        // 所有格式都失败，抛出异常
        throw new IOException("Failed to parse date: " + value + 
            ". Supported formats: " + String.join(", ", DATE_PATTERNS));
    }
}
