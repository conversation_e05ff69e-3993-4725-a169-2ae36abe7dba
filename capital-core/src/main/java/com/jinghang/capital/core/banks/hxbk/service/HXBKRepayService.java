package com.jinghang.capital.core.banks.hxbk.service;

import com.jinghang.capital.core.banks.AbstractBankRepayService;
import com.jinghang.capital.core.banks.hxbk.callback.dto.RepayResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.dto.repay.*;
import com.jinghang.capital.core.banks.hxbk.remote.HXBKRequestService;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.HXBKBankRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.repay.FinRepayService;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.util.DateUtil;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.repay.*;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/07/08
 */
@Service
@Qualifier("HXBKRepayService")
public class HXBKRepayService extends AbstractBankRepayService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKRepayService.class);
    private static final int TWO = 2;
    private static final int PROCESSING_POINT_NUM = 6;
    private static final int YEAR_DAYS = 360;
    private final Integer repayQueryDdlTime = 120000;
    @Autowired
    private HXBKRequestService hxbkRequestService;
    @Autowired
    private HXBKConfig hxbkConfig;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private HXBKBankRepository hxbkBankRepository;
    @Autowired
    private MqService mqService;
    @Autowired
    private FinRepayService finRepayService;
    @Autowired
    private CommonService commonService;

    @Override
    public TrailResultVo trail(RepayTrailVo repayTrailVo) {
        Integer period = repayTrailVo.getPeriod();//期数
        RepayPurpose repayPurpose = repayTrailVo.getRepayPurpose();//还款模式
        LocalDate transferDate = repayTrailVo.getTransferDate();//转账时间
        //通过资金系统放款ID和业务系统放款ID获取借款信息
        Loan loan = getFinRepayService().getLoan(repayTrailVo.getLoanId(), repayTrailVo.getOuterLoanId());
        if (Objects.equals(repayPurpose,RepayPurpose.CURRENT)) {
            //当期试算
            return trialCurrent(loan, period, transferDate);
        }
        //结清还款试算
        return trialClear(loan, period);
    }

    /**
     * 当期试算
     */
    private TrailResultVo trialCurrent(Loan loan, final Integer period, LocalDate transferDate) {
        logger.info("湖消直连当期还款试算，loanId: {}", loan.getId());
        if (Objects.isNull(period)) {
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR.getCode(), "还款期数不能为空");
        }
        //通过借款主键和还款期数获取还款计划信息
        LoanReplan curPlan = getFinRepayService().getRepayPlan(loan.getId(), period);
        // 代偿后
        if (Objects.equals(curPlan.getBankRepayStatus(),RepayStatus.REPAID)) {
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            BigDecimal overdueFee = curPlan.getPenaltyAmt();
            //实还金额大于0, 代表代偿文件已经处理了
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //判断是否已经回购
                if (Objects.equals(RepayPurpose.CLEAR,bankRepayPlan.getRepayPurpose())) {
                    //通过借款主键获取对资还款记录信息列表
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    //获取列表最大期数的对资还款记录
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购之后的期数,也不需要调资方试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        return getInnerTrailResult(curPlan, overdueFee);
                    }
                }
                //调资方试算接口 获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrailResult(loan, transferDate, curPlan);
                overdueFee = remoteTrailResult.getOverdueFee();//罚息
            }
            //不调资方试算
            return getInnerTrailResult(curPlan, overdueFee);
        } else {
            //调资方试算接口
            return getRemoteTrailResult(loan, transferDate, curPlan);
        }
    }

    /**
     * 不调资方试算
     * @param curPlan 还款计划信息
     * @param overdueFee 罚息
     * @return 试算后的信息
     */
    private TrailResultVo getInnerTrailResult(LoanReplan curPlan, BigDecimal overdueFee) {
        TrailResultVo resultVo = new TrailResultVo();
        resultVo.setAmount(
            sumAmount(
                curPlan.getPrincipalAmt(),
                curPlan.getInterestAmt(),
                curPlan.getGuaranteeAmt(),
                overdueFee,
                curPlan.getBreachAmt()));//还款总金额
        resultVo.setPrincipal(curPlan.getPrincipalAmt());//还款本金
        resultVo.setInterest(curPlan.getInterestAmt());//还款利息
        resultVo.setGuaranteeFee(curPlan.getGuaranteeAmt());//还款手续费
        resultVo.setOverdueFee(overdueFee);//还款罚息
        resultVo.setBreachFee(curPlan.getBreachAmt());//还款提结违约金
        resultVo.setPayee(Payee.GUARANTEE);
        return resultVo;
    }

    /**
     * 调资方试算接口
     * @param loan 借款信息
     * @param transferDate 转账时间
     * @param curPlan 还款计划
     * @return 试算后的信息
     */
    private TrailResultVo getRemoteTrailResult(Loan loan, LocalDate transferDate, LoanReplan curPlan) {
        //当期还款试算
        HXBKRepayTrialRequest req = new HXBKRepayTrialRequest();
        req.setOrderNo(IdGenUtil.genReqNo());//订单号-超捷生成
        req.setOriginalOrderNo(loan.getId());//用信申请订单号
        String repayType = "4";//还款类型,默认不在宽限期内，传4
        Integer hxbkOverDueDayAdd = hxbkConfig.getHxbkOverDueDayAdd();//湖消逾期天数配置
        if(Objects.nonNull(transferDate)){
            //逾期天数
            long overDay = DateUtil.dateDiff(curPlan.getRepayDate(), transferDate);
            logger.info("逾期天数为：" + overDay + "，还款计划的应还时间为：" + curPlan.getRepayDate() + "，转账时间为：" + transferDate);
            if (overDay <= 3 + hxbkOverDueDayAdd) {
                //说明在宽限期内，传2
                repayType = "2";//还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
            }
        }else{
            logger.info("===========转账时间为空==============");
        }
        req.setRepayType(repayType);//还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
        logger.info("湖消直连当期还款试算请求: {}", JsonUtil.toJsonString(req));
        HXBKRepayTrialResponse resp = hxbkRequestService.repayTrial(req);
        logger.info("湖消直连当期还款试算结果: {}", JsonUtil.toJsonString(resp));
        //应还本金
        BigDecimal principalAmt = Objects.isNull(resp.getRealPrincipal()) ? BigDecimal.ZERO : resp.getRealPrincipal();
        //应还利息
        BigDecimal intAmt = Objects.isNull(resp.getRealInterest()) ? BigDecimal.ZERO : resp.getRealInterest();
        //应还罚息
        BigDecimal odIntAmt = Objects.isNull(resp.getRealOverAmt()) ? BigDecimal.ZERO : resp.getRealOverAmt();
        TrailResultVo resultVo = new TrailResultVo();
        //应还总金额 = 应还本金+应还利息+应还资方罚息+应还融担费(我司)
        resultVo.setAmount(
            sumAmount(
                principalAmt,
                intAmt,
                odIntAmt,
                resp.getRealGuaranteeFee(),
                resp.getRealLiquidatedDamages()));//还款总金额
        resultVo.setPrincipal(principalAmt);//还款本金
        resultVo.setInterest(intAmt);//还款利息
        resultVo.setGuaranteeFee(safeNum(resp.getRealGuaranteeFee()));//担保费
        resultVo.setOverdueFee(odIntAmt);//还款罚息
        resultVo.setBreachFee(safeNum(resp.getRealLiquidatedDamages()));//违约金
        resultVo.setPayee(Payee.CAPITAL);
        return resultVo;
    }

    private TrailResultVo trialClear(Loan loan, final Integer period) {
        logger.info("湖消直连结清还款试算，loanId: {}, period: {}", loan.getId(), period);
        clearTrailCheck(loan, period);
        // 对客未还list
        List<LoanReplan> customUnpaidList = getFinRepayService().findRepayPlanByLoanIdAndCustomStatus(loan.getId(), RepayStatus.NORMAL);
        // 对客未还最小期
        LoanReplan minPlan = customUnpaidList.stream().min(Comparator.comparing(LoanReplan::getPeriod))
            .orElseThrow(() -> new BizException(BizErrorCode.CUSTOM_UNPAID_PLAN_NOT_FOUND));
        // 对资未还最小期
        LoanReplan curBankMinPlan = customUnpaidList.stream().filter(p -> p.getBankRepayStatus() == RepayStatus.NORMAL)
            .min(Comparator.comparing(LoanReplan::getPeriod)).orElse(null);
        // 传过来的期数和对客未还期数不一致
        if (Objects.nonNull(period) && !minPlan.getPeriod().equals(period)) {
            // 与对资未还期数不一致
            Integer capitalCurPeriod = Objects.nonNull(curBankMinPlan) ? curBankMinPlan.getPeriod() : 0;
            if (!period.equals(capitalCurPeriod)) {
                throw new BizException(BizErrorCode.REPAY_TRIAL_CLEAR_PERIOD_ERROR);
            }
        }
        // 计算结清累计金额
        BigDecimal existTotal = BigDecimal.ZERO;
        BigDecimal existPrincipal = BigDecimal.ZERO;
        BigDecimal existInterest;
        BigDecimal existGuarantee;
        BigDecimal existPenalty = BigDecimal.ZERO;
        BigDecimal existBreach = BigDecimal.ZERO;
        for (LoanReplan p : customUnpaidList) {
            existTotal = existTotal.add(safeNum(p.getTotalAmt()));
            existPrincipal = existPrincipal.add(safeNum(p.getPrincipalAmt()));
            existPenalty = existPenalty.add(safeNum(p.getPenaltyAmt()));
            existBreach = existBreach.add(safeNum(p.getBreachAmt()));
        }
        //资金占用天数
        long principalUseDays = getPrincipalUseDays(minPlan, loan);
        //按日计算融担费
        existGuarantee = getGuaranteeAmt(loan, minPlan, existPrincipal, (int) principalUseDays);
        // 对资已结清
        if (Objects.isNull(curBankMinPlan)) {
            // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
            existInterest = getClearInterest(loan, (int) principalUseDays, existPrincipal);
            //资方罚息
            BigDecimal overdueFee = existPenalty;
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            //实还金额大于0 ,代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //判断回购
                if (Objects.equals(RepayPurpose.CLEAR,bankRepayPlan.getRepayPurpose())) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购后的期数,不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        TrailResultVo resultVo = new TrailResultVo();
                        resultVo.setPayee(Payee.GUARANTEE);
                        resultVo.setAmount(
                            sumAmount(
                                existPrincipal,
                                existInterest,
                                existGuarantee,
                                overdueFee,
                                existBreach));
                        resultVo.setPrincipal(existPrincipal);
                        resultVo.setInterest(existInterest);
                        resultVo.setGuaranteeFee(existGuarantee);
                        resultVo.setOverdueFee(overdueFee);
                        resultVo.setBreachFee(existBreach);
                        return resultVo;
                    }
                }
                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrialClearResult(loan, existGuarantee, existBreach);
                overdueFee = remoteTrailResult.getOverdueFee();
            }
            TrailResultVo resultVo = new TrailResultVo();
            resultVo.setPayee(Payee.GUARANTEE);
            resultVo.setAmount(
                sumAmount(
                    existPrincipal,
                    existInterest,
                    existGuarantee,
                    overdueFee,
                    existBreach));
            resultVo.setPrincipal(existPrincipal);
            resultVo.setInterest(existInterest);
            resultVo.setGuaranteeFee(existGuarantee);
            resultVo.setOverdueFee(overdueFee);
            resultVo.setBreachFee(existBreach);
            return resultVo;
        }
        return getRemoteTrialClearResult(loan, existGuarantee, existBreach);
    }

    private TrailResultVo getRemoteTrialClearResult(Loan loan, BigDecimal existGuarantee, BigDecimal existBreach) {
        //全部结清试算
        HXBKRepayTrialRequest req = new HXBKRepayTrialRequest();
        req.setOrderNo(IdGenUtil.genReqNo());//订单号-超捷生成
        req.setOriginalOrderNo(loan.getId());//用信申请订单号
        //提前结清对应正常还款，传2。一次性提前结清对应全部结清，传1
        req.setRepayType("1");//还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
        logger.info("湖消直连结清还款试算请求: {}", JsonUtil.toJsonString(req));
        //湖消调用还款试算接口
        HXBKRepayTrialResponse resp = hxbkRequestService.repayTrial(req);
        logger.info("湖消直连结清还款试算结果: {}", JsonUtil.toJsonString(resp));
        //应还本金
        BigDecimal principalAmt = Objects.isNull(resp.getRealPrincipal()) ? BigDecimal.ZERO : resp.getRealPrincipal();
        //应还利息
        BigDecimal intAmt = Objects.isNull(resp.getRealInterest()) ? BigDecimal.ZERO : resp.getRealInterest();
        //应还罚息
        BigDecimal odIntAmt = Objects.isNull(resp.getRealOverAmt()) ? BigDecimal.ZERO : resp.getRealOverAmt();
        TrailResultVo resultVo = new TrailResultVo();
        resultVo.setPayee(Payee.CAPITAL);
        //应还总金额 = 应还本金+应还利息+应还资方罚息+应还融担费(我司)
        resultVo.setAmount(
            sumAmount(
                principalAmt,
                intAmt,
                odIntAmt,
                existGuarantee,
                existBreach));
        resultVo.setPrincipal(principalAmt);
        resultVo.setInterest(intAmt);
        resultVo.setGuaranteeFee(existGuarantee);
        resultVo.setOverdueFee(odIntAmt);
        resultVo.setBreachFee(existBreach);
        return resultVo;
    }

    /**
     * 统计资金占用天数
     * @param minPlan 还款计划信息
     * @param loan 借款信息
     * @return 资金占用天数
     */
    private long getPrincipalUseDays(LoanReplan minPlan, Loan loan) {
        //资金占用天数
        long days;
        if (Objects.equals(minPlan.getPeriod(),1)) {
            //放款日到当前
            days = ChronoUnit.DAYS.between(loan.getLoanTime().toLocalDate(), LocalDate.now());
        } else {
            //上期还款日到今天
            LoanReplan repayPlan = getFinRepayService().getRepayPlan(loan.getId(), minPlan.getPeriod() - 1);
            days = ChronoUnit.DAYS.between(repayPlan.getRepayDate(), LocalDate.now());
        }
        logger.info("还款计划, Id:{}, 提前结清资金当月占用天数: {}", minPlan.getId(), days);
        return days;
    }

    /**
     * 计算提前结清利息
     * @param loan 借款信息
     * @param principalUseDays 资金占用天数
     * @param existPrincipal 剩余本金
     * @return 提前结清利息
     */
    private BigDecimal getClearInterest(Loan loan, int principalUseDays, BigDecimal existPrincipal) {
        // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
        return loan.getBankRate().divide(new BigDecimal(YEAR_DAYS), PROCESSING_POINT_NUM, RoundingMode.HALF_UP)
            .multiply(new BigDecimal(principalUseDays)).multiply(existPrincipal).setScale(TWO, RoundingMode.HALF_UP);
    }

    /**
     * 提前结清担保费= 剩余本金*资金实际使用天数*【融担费年化利率/360】
     * 融担费年化利率 = 23.95% - 资方利率(湖消目前15%)
     * @param loan 借款信息
     * @param minPlan 还款计划信息
     * @return 提前结清融担费
     */
    public BigDecimal getGuaranteeAmt(Loan loan, LoanReplan minPlan, BigDecimal existPrincipal, int principalDays) {
        //融担日利率 = (对客利率 - 对资利率) / 360
        BigDecimal guaranteeRate = loan.getCustomRate().subtract(loan.getBankRate()).divide(
            new BigDecimal(YEAR_DAYS), PROCESSING_POINT_NUM, RoundingMode.HALF_UP);
        // 提前结清融担费 = 剩余本金 * 资金实际占用天数 * 融担费日利率
        BigDecimal guaranteeFee = existPrincipal.multiply(new BigDecimal(principalDays)).multiply(guaranteeRate).setScale(2, RoundingMode.HALF_UP);
        logger.info("借据, loanId: {}, 开始期数: {}, 提前结清担保费: {}", loan.getId(), minPlan.getPeriod(), guaranteeFee);
        return guaranteeFee;
    }

    /**
     * 湖消直连线上还款
     * @param customerRepayRecord 对客还款记录信息
     * @param repayPlan 还款计划信息
     * @return 还款结果
     */
    @Override
    public RepayResultVo onlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan repayPlan, RepayApplyVo repayApplyDto) {
        logger.info("湖消直连线上还款销账:loanId:{},period:{}", repayPlan.getLoanId(), repayPlan.getPeriod());
        String loanId = repayPlan.getLoanId();
        Integer period = repayPlan.getPeriod();
        //代偿后还款
        if (getFinRepayService().existClaimedRepayPlan(loanId, period)) {
            //对客还款计划更新成功
            doCustomReplanSuccess(customerRepayRecord.getId());
            if (Objects.equals(customerRepayRecord.getRepayPurpose(),RepayPurpose.CLEAR)) {
                //提前结清,更新剩余期数对客状态为成功
                modifyClearCustomStatus(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
            }
            //对资罚息
            customerRepayRecord.setBankPenaltyAmt(repayPlan.getPenaltyAmt());
            //对资已代偿,不需要接口通知,直接返回成功
            return repayAfterClaim();
        }
        //对资代偿前还款, 正常还款
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);
        //还款前 调资方还款试算
        TrailResultVo trailResult = trail(repayTrailVo);
        //初始化 对资还款记录
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee()); //对资应还罚息
        bankRepayRecord.setBreachAmt(trailResult.getBreachFee()); //应还违约金
        bankRepayRecord.setTotalAmt(
                sumAmount(bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setChargeChannelId(customerRepayRecord.getChargeChannelId());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);
        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());
        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());
        bankRepayRecord.setPayOrderNo(customerRepayRecord.getPayOrderNo());
        //拼装银行卡信息
        String repayBankAbbr = customerRepayRecord.getRepayBankCode();//还款银行编码
        applyVo.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        applyVo.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        applyVo.setRepayBankCode(repayBankAbbr);
        applyVo.setAgreementNo(customerRepayRecord.getAgreementNo());
        applyVo.setTransferDate(repayApplyDto.getTransferDate());//转账时间
        //调用资方还款接口
        return bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());
    }

    /**
     * 湖消直连线下还款
     * @param customerRepayRecord 对客还款记录信息
     * @param replan 还款计划信息
     * @return 还款结果
     */
    @Override
    public RepayResultVo offlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan replan, RepayApplyVo repayApplyDto) {
        logger.info("湖消直连线下还款销账:loanId:{},period:{}", replan.getLoanId(), replan.getPeriod());
        //对客还款计划更新成功
        doCustomReplanSuccess(customerRepayRecord.getId());
        //对资已代偿,不需要接口通知,直接返回成功
        if (Objects.equals(RepayStatus.REPAID,replan.getBankRepayStatus())) {
            if (Objects.equals(customerRepayRecord.getRepayPurpose(),RepayPurpose.CLEAR)) {
                //线下提前结清,更新剩余期数对客状态为成功
                modifyClearCustomStatus(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
            }
            //对资罚息
            customerRepayRecord.setBankPenaltyAmt(replan.getPenaltyAmt());
            //对资已代偿,不需要接口通知,直接返回成功
            return repayAfterClaim();
        }
        RepayResultVo repayResultVo = new RepayResultVo();
        repayResultVo.setStatus(ProcessStatus.SUCCESS);
        String loanId = replan.getLoanId();
        Integer period = replan.getPeriod();
        RepayResultVo bankRepayResult;
        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);
        //还款试算
        TrailResultVo trailResult = directTrail(repayTrailVo);
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee());
        bankRepayRecord.setBreachAmt(BigDecimal.ZERO);
        bankRepayRecord.setTotalAmt(
                sumAmount(
                        bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setReduceAmount(customerRepayRecord.getReduceAmt());
        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());
        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);
        applyVo.setTransferDate(repayApplyDto.getTransferDate());//转账时间
        //调用资方还款接口
        bankRepayResult = bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());
        if (Objects.equals(bankRepayResult.getStatus(),ProcessStatus.FAIL)) {
            getWarningService().warn("线下还款，通知湖消直连失败. loanId: " + loanId + ", period: " + period);
        }
        return repayResultVo;
    }

    /**
     * CORE内部使用-还款试算
     */
    public TrailResultVo directTrail(RepayTrailVo trailVo) {
        Loan loan = getFinRepayService().getLoan(trailVo.getLoanId(), trailVo.getOuterLoanId());
        LoanReplan curPlan = getFinRepayService().getRepayPlan(loan.getId(), trailVo.getPeriod());
        if (Objects.equals(trailVo.getRepayPurpose(),RepayPurpose.CURRENT)) {
            //湖消直连当期正常还款试算
            return directCurTrail(loan, curPlan, trailVo.getTransferDate());
        }
        //湖消直连全部结清还款试算
        return directClearTrail(loan, curPlan);
    }

    /**
     * 代偿后还款
     */
    public RepayResultVo repayAfterClaim() {
        // 直接成功
        RepayResultVo resultVo = new RepayResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        resultVo.setActRepayTime(LocalDateTime.now());
        return resultVo;
    }

    /**
     * 调用资方还款接口
     */
    @Override
    public RepayResultVo bankRepayApply(RepayApplyVo repayApplyDto, BankRepayRecord bankRepayRecord, BigDecimal reduceAmt, String custRepayRecordId) {
        RepayResultVo result;
        String loanId = repayApplyDto.getLoanId();
        Loan loan = getFinRepayService().getLoan(loanId, null);
        LoanReplan originPlan = getFinRepayService().getRepayPlan(loanId, repayApplyDto.getPeriod());
        //判断对资还款状态是否为已代偿
        if (Objects.equals(originPlan.getBankRepayStatus(),RepayStatus.REPAID)) {
            // 对资已代偿,不需要接口通知,直接返回成功
            getWarningService().warn("湖消直连 代偿后不需要通知资方 customerRepayRecordId:" + custRepayRecordId);
            result = new RepayResultVo();
            result.setStatus(ProcessStatus.FAIL);
            return result;
        }
        if (Objects.isNull(bankRepayRecord)) {
            //异常：还款记录不存在
            throw new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND);
        }
        RepayPurpose repayPurpose = repayApplyDto.getRepayPurpose();
        //湖消还款类型：1: 全部结清；2：正常还款 3：当期结清4：逾期还款。01→2和04→4对应CURRENT为当期传2，03→1对应CLEAR为结清传1
        if (Objects.equals(repayPurpose,RepayPurpose.CURRENT)) {//当期
            BigDecimal penaltyAmt = bankRepayRecord.getPenaltyAmt();//罚息
            //检验还款试算罚息不为空并且大于0，为true,走逾期还款，否则走正常还款
            if(Objects.nonNull(penaltyAmt) && penaltyAmt.compareTo(BigDecimal.ZERO) > 0){
                String typeLogMsg = "逾期";//还款类型日志信息
                String repayType = "4";//还款类型,默认传4,防止转账时间为空
                Integer hxbkOverDueDayAdd = hxbkConfig.getHxbkOverDueDayAdd();//湖消逾期天数配置
                LocalDate transferDate = repayApplyDto.getTransferDate();
                if(Objects.nonNull(transferDate)){
                    //逾期天数
                    long overDay = DateUtil.dateDiff(originPlan.getRepayDate(), transferDate);
                    logger.info("逾期天数为：" + overDay + "，还款计划的应还时间为：" + originPlan.getRepayDate() + "，转账时间为：" + transferDate);
                    if (overDay <= 3 + hxbkOverDueDayAdd) {
                        //说明在宽限期内，传2
                        repayType = "2";//还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
                        typeLogMsg = "正常";
                    }
                }else{
                    logger.info("===========转账时间为空，走的逾期还款==============");
                }
                logger.info("湖消直连" + typeLogMsg + "还款调用还款接口==============开始");
                //逾期还款调用还款接口方法
                result = repayNormalAndClear(loan, bankRepayRecord, repayType);
                logger.info("湖消直连" + typeLogMsg + "还款调用还款接口==============结束");
            }else{
                logger.info("湖消直连正常还款调用还款接口==============开始");
                //正常还款调用还款接口方法
                result = repayNormalAndClear(loan, bankRepayRecord, "2");
                logger.info("湖消直连正常还款调用还款接口==============结束");
            }
        } else if (Objects.equals(repayPurpose,RepayPurpose.CLEAR)) {//结清
            logger.info("湖消直连全部结清调用还款接口==============开始");
            //全部结清调用还款接口方法
            result = repayNormalAndClear(loan, bankRepayRecord, "1");
            logger.info("湖消直连全部结清调用还款接口==============结束");
        } else {
            logger.info("湖消直连还款申请时，当前还款类型:{}不支持还款申请，请检查！", repayPurpose);
            //如果是不支持的还款类型，提示异常信息：湖消直连还款申请时，当前还款类型:xx不支持还款申请，请检查！
            throw new BizException("70046", "湖消直连还款申请时，当前还款类型:" + repayPurpose + "不支持还款申请，请检查！");
        }
        //以查询结果为终态
        bankRepayRecord.setRepayStatus(ProcessStatus.PROCESSING);
        //更新对资还款记录信息
        getFinRepayService().updateBankRepayRecord(bankRepayRecord);
        getMqService().submitRepayQueryDelay(bankRepayRecord.getId(), null, repayQueryDdlTime);
        return result;
    }

    /**
     * 正常还款或全部结清调用还款接口方法
     * @param loan 借款信息
     * @param bankRepayRecord 对资还款记录信息
     * @param repayType 还款类型
     * @return 还款结果
     */
    public RepayResultVo repayNormalAndClear(Loan loan, BankRepayRecord bankRepayRecord, String repayType) {
        HXBKRepayParamDTO repayParamDTO = new HXBKRepayParamDTO();
        repayParamDTO.setOrderNo(bankRepayRecord.getId());//订单号
        repayParamDTO.setOriginalOrderNo(loan.getId());//用信申请订单号
        repayParamDTO.setRepayType(repayType);//还款类型
        BigDecimal totalAmt = bankRepayRecord.getTotalAmt();//还款总金额
        repayParamDTO.setValidRepayAmount(Objects.isNull(totalAmt) ? BigDecimal.ZERO : totalAmt);//校验还款金额
        //判断还款模式是线上还是线下，Y：线上还款；N:线下还款
        if (Objects.equals(bankRepayRecord.getRepayMode(), RepayMode.OFFLINE)) {//线下还款
            repayParamDTO.setPaymentFlag("N");//是否支付代扣
        } else {//线上还款
            repayParamDTO.setPaymentFlag("Y");//是否支付代扣
        }
        repayParamDTO.setBankCardNo(bankRepayRecord.getRepayAcctNo());//银行卡号
        repayParamDTO.setMerchantNo(hxbkConfig.getMerchantShop());//平台商户号（母订单商户号）
        repayParamDTO.setPlatformSerialNo(bankRepayRecord.getPayOrderNo());//平台交易流水号(母订单号)
        repayParamDTO.setReservedMobile(bankRepayRecord.getRepayRelPhone());//银行预留手机号
        repayParamDTO.setDeductionCode("BAOFUPAY");//扣款渠道代码:BAOFUPAY：宝付（默认）
        repayParamDTO.setSigningAgreementNum(bankRepayRecord.getAgreementNo());//通道签约协议号
        String repayBankCode = bankRepayRecord.getRepayBankCode();//还款银行编码
        if(StringUtils.isBlank(repayBankCode)){
            logger.info("湖消直连还款时，还款银行编码为空。请检查！");
            //还款银行编码判空检验，为空。提示：湖消直连还款时，还款银行编码为空。请检查！
            throw new BizException(BizErrorCode.BANK_CODE_IS_NULL_ERROR);
        }
        //湖消直连通过银行编码获取银行名称
        HXBKBankList hxbkBankList = hxbkBankRepository.findBankByBankCode(repayBankCode);
        if (Objects.nonNull(hxbkBankList)) {
            String isSupRepayFlg = hxbkBankList.getIsSupRepayFlg();//是否支持还款标志(默认N)：Y:是 N:否
            String bankName = hxbkBankList.getBankName();//银行名称
            //检验该银行是否支持还款
            if(Objects.equals(isSupRepayFlg,"Y")){//支持
                repayParamDTO.setAccountName(bankName);//账号开户名
                repayParamDTO.setAccountOpenBank(repayBankCode);//账号开户行
            }else{//不支持
                logger.info("{}不属于支持还款的银行，请检查！", bankName);
                //如果是不支持还款的银行，提示异常信息：xx银行不属于支持还款的银行，请检查！
                throw new BizException("70043", bankName + "不属于支持还款的银行，请检查！");
            }
        }else{
            logger.info("通过还款银行编码({})没有查询到湖消的银行信息。请检查！", repayBankCode);
            //湖消直连通过还款银行编码没有查询到湖消的银行信息时，提示：通过还款银行编码(具体银行编码)没有查询到湖消的银行信息。请检查！
            throw new BizException("70044", "通过还款银行编码(" + repayBankCode + ")没有查询到湖消的银行信息。请检查！");
        }
        //检验是否支付代扣为是时的必填项字段
        if(Objects.equals(repayParamDTO.getPaymentFlag(),"Y")){
            if(StringUtils.isBlank(repayParamDTO.getBankCardNo()) || StringUtils.isBlank(repayParamDTO.getMerchantNo())
            || StringUtils.isBlank(repayParamDTO.getPlatformSerialNo()) || StringUtils.isBlank(repayParamDTO.getAccountName())
            || StringUtils.isBlank(repayParamDTO.getReservedMobile())){
                logger.info("湖消直连线上还款是否支付代扣为是时，检验必填项不通过。请检查！");
                //必填项检验不通过，提示异常信息：湖消直连线上还款是否支付代扣为是时，检验必填项不通过。请检查！
                throw new BizException(BizErrorCode.ONLINE_REPAYMENT_CHECK_ERROR);
            }
        }
        RepayResultVo resultVo = new RepayResultVo();
        //封装还款申请接口参数
        HXBKRepayApplyRequest request = buildRepayParam(repayParamDTO);
        logger.info("湖消直连调用还款接口的请求参数={}", JsonUtil.toJsonString(request));
        HXBKRepayApplyResponse resp;
        try {
            //湖消调用还款接口
            resp = hxbkRequestService.repayApply(request);
            logger.info("湖消直连调用还款接口的返回结果={}", JsonUtil.toJsonString(resp));
        } catch (BizException ex) {
            logger.error("湖消直连调用还款接口返回结果异常", ex);
        }
        // 还款申请 一律以查询接口为最终结果
        resultVo.setStatus(ProcessStatus.PROCESSING);
        return resultVo;
    }

    /**
     * 湖消直连当期正常还款试算
     * @param loan 借款信息
     * @param plan 还款计划信息
     * @param transferDate 转账时间
     * @return 试算结果
     */
    private TrailResultVo directCurTrail(Loan loan, LoanReplan plan, LocalDate transferDate) {
        if (Objects.equals(plan.getBankRepayStatus(),RepayStatus.REPAID)) {
            //代偿后
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), plan.getPeriod());
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            BigDecimal overdueFee = plan.getPenaltyAmt();
            //实还金额大于0, 代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                if (Objects.equals(RepayPurpose.CLEAR,bankRepayPlan.getRepayPurpose())) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购之后的期数不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        return getInnerTrailResult(plan, overdueFee);
                    }
                }
                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrailResult(loan, transferDate, plan);
                overdueFee = remoteTrailResult.getOverdueFee();
            }
            // 不调资方试算
            return getInnerTrailResult(plan, overdueFee);
        }
        //调用资方试算
        return getRemoteTrailResult(loan, transferDate, plan);
    }

    /**
     * 湖消直连全部结清还款试算
     * @param loan 借款信息
     * @param plan 还款计划信息
     * @return 试算结果
     */
    private TrailResultVo directClearTrail(Loan loan, LoanReplan plan) {
        logger.info("请求湖消直连结清试算:loanId:{},period:{}", plan.getLoanId(), plan.getPeriod());
        Integer period = plan.getPeriod();
        // 对客未还list
        List<LoanReplan> bankUnpaidList = getFinRepayService().findRepayPlanByLoanIdAndBankStatus(loan.getId(), RepayStatus.NORMAL);
        // 计算结清累计金额
        BigDecimal existTotal = BigDecimal.ZERO;
        BigDecimal existPrincipal = BigDecimal.ZERO;
        BigDecimal existInterest;
        BigDecimal existGuarantee;
        BigDecimal existPenalty = BigDecimal.ZERO;
        BigDecimal existBreach = BigDecimal.ZERO;
        for (LoanReplan p : bankUnpaidList) {
            existTotal = existTotal.add(safeNum(p.getTotalAmt()));
            existPrincipal = existPrincipal.add(safeNum(p.getPrincipalAmt()));
            existPenalty = existPenalty.add(safeNum(p.getPenaltyAmt()));
            existBreach = existBreach.add(safeNum(p.getBreachAmt()));
        }
        //资金占用天数
        long principalUseDays = getPrincipalUseDays(plan, loan);
        // 融担费按日计算
        existGuarantee = getGuaranteeAmt(loan, plan, existPrincipal, (int) principalUseDays);
        // 代偿后
        if (Objects.equals(RepayStatus.REPAID,plan.getBankRepayStatus())) {
            // 提前结清利息=资方利率/360*资金实际使用天数*剩余本金
            existInterest = getClearInterest(loan, (int) principalUseDays, existPrincipal);
            //资方罚息
            BigDecimal overdueFee = existPenalty;
            BankLoanReplan bankRepayPlan = getFinRepayService().findBankRepayPlan(loan.getId(), period);
            BigDecimal actTotalAmt = Objects.nonNull(bankRepayPlan.getActTotalAmt()) ? bankRepayPlan.getActTotalAmt() : BigDecimal.ZERO;
            //实还金额大于0,代表代偿文件已处理
            if (actTotalAmt.compareTo(BigDecimal.ZERO) == 0) {
                //回购判断
                if (Objects.equals(RepayPurpose.CLEAR,bankRepayPlan.getRepayPurpose())) {
                    List<BankRepayRecord> successRecord = bankRepayRecordRepository.findByLoanIdAndRepayStatus(loan.getId(), ProcessStatus.SUCCESS);
                    BankRepayRecord maxRecord = successRecord.stream().max(Comparator.comparing(BankRepayRecord::getPeriod)).orElseThrow();
                    //回购后的期数  不需要试算
                    if (bankRepayPlan.getPeriod() > maxRecord.getPeriod()) {
                        // 不调资方试算
                        TrailResultVo resultVo = new TrailResultVo();
                        resultVo.setPayee(Payee.GUARANTEE);
                        resultVo.setAmount(
                                sumAmount(
                                        existPrincipal,
                                        existInterest,
                                        existGuarantee,
                                        overdueFee,
                                        existBreach));
                        resultVo.setPrincipal(existPrincipal);
                        resultVo.setInterest(existInterest);
                        resultVo.setGuaranteeFee(existGuarantee);
                        resultVo.setOverdueFee(overdueFee);
                        resultVo.setBreachFee(existBreach);
                        return resultVo;
                    }
                }
                //资方代偿当日,代偿文件解析前 需要调资方试算接口获取罚息
                TrailResultVo remoteTrailResult = getRemoteTrialClearResult(loan, existGuarantee, existBreach);
                overdueFee = remoteTrailResult.getOverdueFee();
            }
            TrailResultVo resultVo = new TrailResultVo();
            resultVo.setPayee(Payee.GUARANTEE);
            resultVo.setAmount(
                    sumAmount(
                            existPrincipal,
                            existInterest,
                            existGuarantee,
                            overdueFee,
                            existBreach));
            resultVo.setPrincipal(existPrincipal);
            resultVo.setInterest(existInterest);
            resultVo.setGuaranteeFee(existGuarantee);
            resultVo.setOverdueFee(overdueFee);
            resultVo.setBreachFee(existBreach);
            return resultVo;
        }
        //调资方结清试算
        return getRemoteTrialClearResult(loan, existGuarantee, existBreach);
    }

    /**
     * 人工手动触发还款申请
     * @param customerRepayRecord 对客还款记录信息
     */
    @Override
    public void bankRepayNotify(CustomerRepayRecord customerRepayRecord) {
        LoanReplan repayPlan = getFinRepayService().getRepayPlan(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
        logger.info("湖消直连 重推线下还款销账:loanId:{},period:{}", repayPlan.getLoanId(), repayPlan.getPeriod());
        if (RepayMode.ONLINE.equals(customerRepayRecord.getRepayMode())) {
            getWarningService().warn("湖消直连 不允许重推线上还款 customerRepayRecordId:" + customerRepayRecord.getId());
            return;
        }
        if (RepayStatus.REPAID.equals(repayPlan.getBankRepayStatus())) {
            //对资已代偿,不需要接口通知,直接返回成功
            getWarningService().warn("湖消直连 代偿后不需要通知资方 customerRepayRecordId:" + customerRepayRecord.getId());
            return;
        }
        String loanId = repayPlan.getLoanId();
        Integer period = repayPlan.getPeriod();
        RepayResultVo bankRepayResult;
        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        applyVo.setRepayMode(customerRepayRecord.getRepayMode());
        applyVo.setRepayType(customerRepayRecord.getRepayType());
        applyVo.setTransferDate(customerRepayRecord.getTransferDate());
        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);
        repayTrailVo.setTransferDate(customerRepayRecord.getTransferDate());
        //还款试算
        TrailResultVo trailResult = directTrail(repayTrailVo);
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(trailResult.getPrincipal());
        bankRepayRecord.setInterestAmt(trailResult.getInterest());
        bankRepayRecord.setGuaranteeAmt(trailResult.getGuaranteeFee());
        bankRepayRecord.setTotalAmt(
                sumAmount(
                        bankRepayRecord.getPrincipalAmt(),
                        bankRepayRecord.getInterestAmt(),
                        bankRepayRecord.getGuaranteeAmt(),
                        bankRepayRecord.getPenaltyAmt(),
                        bankRepayRecord.getBreachAmt()));
        bankRepayRecord.setPenaltyAmt(trailResult.getOverdueFee());
        bankRepayRecord.setBreachAmt(BigDecimal.ZERO);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setSysId(customerRepayRecord.getId());
        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(customerRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(customerRepayRecord.getRepayMode());
        bankRepayRecord.setRepayType(customerRepayRecord.getRepayType());
        bankRepayRecord.setChannel(customerRepayRecord.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        bankRepayRecord.setReduceAmount(customerRepayRecord.getReduceAmt());
        bankRepayRecord = getFinRepayService().saveBankRepayRecord(bankRepayRecord);
        //保存还款卡信息
        bankRepayRecord.setAgreementNo(customerRepayRecord.getAgreementNo());
        bankRepayRecord.setRepayBankCode(customerRepayRecord.getRepayBankCode());
        bankRepayRecord.setRepayAcctNo(customerRepayRecord.getRepayAcctNo());
        bankRepayRecord.setRepayRelUser(customerRepayRecord.getRepayRelUser());
        bankRepayRecord.setRepayRelPhone(customerRepayRecord.getRepayRelPhone());
        bankRepayRecord.setRepayRelCard(customerRepayRecord.getRepayRelCard());
        applyVo.setTransferDate(customerRepayRecord.getTransferDate());//转账时间
        //还款通知
        bankRepayResult = bankRepayApply(applyVo, bankRepayRecord, customerRepayRecord.getReduceAmt(), customerRepayRecord.getId());
        if (Objects.equals(bankRepayResult.getStatus(),ProcessStatus.FAIL)) {
            getWarningService().warn("线下还款，通知湖消直连失败. loanId: " + loanId + ", period: " + period);
        }
    }

    /**
     * 封装还款申请接口参数
     */
    private HXBKRepayApplyRequest buildRepayParam(HXBKRepayParamDTO repayParamDTO) {
        HXBKRepayApplyRequest req = new HXBKRepayApplyRequest();
        req.setOrderNo(repayParamDTO.getOrderNo());//订单号
        req.setOriginalOrderNo(repayParamDTO.getOriginalOrderNo());//用信申请订单号
        req.setRepayType(repayParamDTO.getRepayType());//还款类型
        req.setValidRepayAmount(repayParamDTO.getValidRepayAmount());//校验还款金额
        req.setBankCardNo(repayParamDTO.getBankCardNo());//银行卡号
        req.setPaymentFlag(repayParamDTO.getPaymentFlag());//是否支付代扣
        req.setMerchantNo(repayParamDTO.getMerchantNo());//平台商户号
        req.setPlatformSerialNo(repayParamDTO.getPlatformSerialNo());//平台交易流水号
        req.setAccountName(repayParamDTO.getAccountName());//账号开户名
        req.setReservedMobile(repayParamDTO.getReservedMobile());//银行预留手机号
        req.setAccountOpenBank(repayParamDTO.getAccountOpenBank());//账号开户行
        req.setDeductionCode(repayParamDTO.getDeductionCode());//扣款渠道代码
        req.setSigningAgreementNum(repayParamDTO.getSigningAgreementNum());//通道签约协议号
        return req;
    }

    /**
     * 湖消直连还款结果查询
     * @param repayId 对资还款记录表主键
     * @return 查询结果
     */
    @Override
    public RepayResultVo bankQuery(String repayId) {
        BankRepayRecord repayRecord = getFinRepayService().getBankRepayRecord(repayId);
        HXBKRepayQueryRequest req = new HXBKRepayQueryRequest();
        req.setOrderNo(IdGenUtil.genReqNo());//订单号-超捷生成
        req.setOriginalOrderNo(repayRecord.getId());//原还款订单号
        logger.info("湖消直连还款结果查询请求={}", JsonUtil.toJsonString(req));
        HXBKRepayQueryResponse resp = hxbkRequestService.repayQuery(req);
        logger.info("湖消直连还款结果查询结果={}", JsonUtil.toJsonString(resp));
        RepayResultVo resultVo = new RepayResultVo();
        String resultCode = resp.getResultCode();//结果code
        /*OK-成功 FAIL-系统异常 ORDER_NOT_EXIST-订单不存在 ORDER_NOT_BELONG-订单不归属该用户 REMOTE_ERROR-远程调用异常
        PARAM_ERROR-参数错误 PARAM_MISS-缺少必要参数 PAYING-支付处理中 PRODUCT_NOTEXIST-产品不存在 APPLY_ERROR-提交失败*/
        if(!Objects.equals(resultCode,"OK")){
            logger.info("湖消直连还款结果查询未成功，请检查！结果code为：" + resultCode + "，结果信息为：" + resp.getResultMsg());
            throw new BizException("70045", "湖消直连还款结果查询未成功，请检查！结果code为：" + resultCode + "，结果信息为：" + resp.getResultMsg());
        }
        resultVo.setBankSeq(resp.getRepayNo());//还款编号作为资方还款流水号
        if (resp.isSuccess()) {//成功
            resultVo.setStatus(ProcessStatus.SUCCESS);//还款状态-成功
            resultVo.setActRepayTime(LocalDateTime.now());//还款成功时间
        } else if (resp.isProcess()) {//处理中
            resultVo.setStatus(ProcessStatus.PROCESSING);//还款状态-处理中
        } else if (resp.isFail()) {//失败
            if (Objects.equals(RepayMode.OFFLINE,repayRecord.getRepayMode())) {
                getWarningService().warn("湖消直联 线下还款失败 bankRepayRecordId: " + repayRecord.getId());
            }
            resultVo.setStatus(ProcessStatus.FAIL);//还款状态-失败
            resultVo.setFailMsg(resp.getFailReason());//失败信息
        } else {
            getWarningService().warn("湖消直连还款结果查询结果异常: 资方返回状态不正确:" + resp.getRepayStatus() + ", BankRepayRecord ID:" + repayId);
            resultVo.setStatus(ProcessStatus.PROCESSING);//还款状态-处理中
        }
        return resultVo;
    }

    /**
     * 还款结果回调业务处理
     * @param request 还款回调请求数据
     */
    public void doRepayResultCallback(RepayResultCallbackRequest request) {
        String orderNo = request.getOrderNo();//原还款订单号(还款传的BankRepayRecord表的主键)
        //通过原还款订单号查询对资还款记录表，没有找到会抛异常：还款记录不存在
        BankRepayRecord bankRepayRecord = getFinRepayService().getBankRepayRecord(orderNo);
        logger.info("还款回调实时调用还款结果查询和还款申请回调请求的还款状态是否一致检验==========开始");
        //还款申请回调请求进来默认先调用还款结果查询接口，判断返回的状态和回调请求的状态是否一致。如果不一致，进行企业微信告警
        HXBKRepayQueryRequest req = new HXBKRepayQueryRequest();
        req.setOrderNo(IdGenUtil.genReqNo());//订单号-超捷生成
        req.setOriginalOrderNo(orderNo);//原还款订单号
        logger.info("湖消直连回调还款结果查询请求={}", JsonUtil.toJsonString(req));
        HXBKRepayQueryResponse resp = hxbkRequestService.repayQuery(req);
        logger.info("湖消直连回调还款结果查询结果={}", JsonUtil.toJsonString(resp));
        String resultCode = resp.getResultCode();//结果code
        /*OK-成功 FAIL-系统异常 ORDER_NOT_EXIST-订单不存在 ORDER_NOT_BELONG-订单不归属该用户 REMOTE_ERROR-远程调用异常
        PARAM_ERROR-参数错误 PARAM_MISS-缺少必要参数 PAYING-支付处理中 PRODUCT_NOTEXIST-产品不存在 APPLY_ERROR-提交失败*/
        if(!Objects.equals(resultCode,"OK")){
            logger.info("湖消直连回调还款结果查询未成功，请检查！结果code为：" + resultCode + "，结果信息为：" + resp.getResultMsg());
            throw new BizException("70045", "湖消直连回调还款结果查询未成功，请检查！结果code为：" + resultCode + "，结果信息为：" + resp.getResultMsg());
        }
        String status = resp.getRepayStatus();//实时调用还款结果查询还款状态
        String repayStatus = request.getRepayStatus();//还款申请回调请求的还款状态
        if(!Objects.equals(status,repayStatus)){
            //不一致，进行企业微信告警
            logger.info("还款回调实时调用还款结果查询和还款申请回调请求的还款状态是否一致检验结果为：不一致，进行企业微信告警");
            getWarningService().warn("还款记录表主键为：" + orderNo + "。还款回调实时调用还款结果查询和还款申请回调请求的还款状态不一致: "
                    + "还款回调实时调用还款结果查询状态为:" + status + ", 还款申请回调请求的状态为:" + repayStatus);
        }else{
            //一致，执行后续逻辑
            logger.info("还款回调实时调用还款结果查询和还款申请回调请求的还款状态是否一致检验结果为：一致，执行后续逻辑");
            RepayResultVo resultVo = new RepayResultVo();
            resultVo.setBankSeq(request.getRepayNo());//还款编号作为资方还款流水号
            if (request.isSuccess()) {//成功
                resultVo.setStatus(ProcessStatus.SUCCESS);//还款状态-成功
                resultVo.setActRepayTime(LocalDateTime.now());//还款成功时间
            } else if (request.isProcess()) {//处理中
                resultVo.setStatus(ProcessStatus.PROCESSING);//还款状态-处理中
            } else if (request.isFail()) {//失败
                if (Objects.equals(RepayMode.OFFLINE,bankRepayRecord.getRepayMode())) {
                    getWarningService().warn("湖消直联 线下还款失败 bankRepayRecordId: " + orderNo);
                }
                resultVo.setStatus(ProcessStatus.FAIL);//还款状态-失败
                resultVo.setFailMsg(request.getFailReason());//失败信息
            } else {
                getWarningService().warn("湖消直连还款结果回调异常: 资方返回状态不正确:" + request.getRepayStatus() + ", BankRepayRecord ID:" + orderNo);
                resultVo.setStatus(ProcessStatus.PROCESSING);//还款状态-处理中
            }
            //还款结果
            if (Objects.equals(ProcessStatus.PROCESSING,resultVo.getStatus())) {//处理中
                mqService.submitRepayQueryDelay(bankRepayRecord.getId());
                return;
            }
            //检验还款类型是否为代还
            if (Objects.equals(RepayType.SUBSTITUTE,bankRepayRecord.getRepayType())) {
                //代还处理
                processSubstitute(bankRepayRecord, resultVo);
                return;
            }
            CustomerRepayRecord customerRepayRecord = commonService.findRepayRecordById(bankRepayRecord.getSysId());
            if (Objects.equals(ProcessStatus.FAIL,resultVo.getStatus())) {//失败
                bankRepayRecord.setRepayStatus(ProcessStatus.FAIL);
                bankRepayRecord.setRemark(resultVo.getFailMsg());
                //更新对资还款记录的还款状态和失败原因
                finRepayService.updateBankRepayRecord(bankRepayRecord);
                if (Objects.equals(RepayMode.ONLINE,customerRepayRecord.getRepayMode())) {
                    customerRepayRecord.setRepayStatus(ProcessStatus.FAIL);
                    customerRepayRecord.setRemark(resultVo.getFailMsg());
                    //更新对客还款记录的还款状态和失败原因
                    finRepayService.updateCustomRepayRecord(customerRepayRecord);
                }
                return;
            }
            if (Objects.equals(ProcessStatus.SUCCESS,resultVo.getStatus())) {//成功
                //还款成功时间
                bankRepayRecord.setRepayTime(resultVo.getActRepayTime());
                bankRepayRecord.setPayOrderNo(Objects.isNull(resultVo.getPayOrderNo()) ? bankRepayRecord.getPayOrderNo() : resultVo.getPayOrderNo());
                //更新资方还款流水号
                if (StringUtil.isBlank(bankRepayRecord.getBankSerial()) && StringUtil.isNotBlank(resultVo.getBankSeq())) {
                    bankRepayRecord.setBankSerial(resultVo.getBankSeq());
                }
                customerRepayRecord.setRepayTime(resultVo.getActRepayTime());
                customerRepayRecord.setBankPenaltyAmt(resultVo.getPenaltyAmt());
                customerRepayRecord.setPayOrderNo(Objects.isNull(resultVo.getPayOrderNo()) ? customerRepayRecord.getPayOrderNo() : resultVo.getPayOrderNo());
                //还款成功后更新处理逻辑
                doRepaySuccess(bankRepayRecord, customerRepayRecord);
                RepayPurpose repayPurpose = bankRepayRecord.getRepayPurpose();//还款模式
                //判断还款模式是否为结清
                if (!Objects.equals(repayPurpose, RepayPurpose.CURRENT)) {
                    //修改结清时剩余期数的状态
                    modifyClearRepayStatus(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
                }
                //更新在贷
                changeOnLoan(bankRepayRecord.getLoanId(), LoanStage.AFTER_LOAN, bankRepayRecord.getPrincipalAmt());
            }
        }
    }

    @Override
    public boolean repayValidate(RepayApplyVo repayApplyVo) {
        return checkYearPeriodNoRepay();
    }

    /**
     * 检验年结期间不还款方法
     * @return 检验结果
     */
    private boolean checkYearPeriodNoRepay() {
        //检验还款黑暗期(22:30-4:00)
        checkRepayDarkPeriod();
        //规定时间格式为时间戳
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String yearPeriodStartTime = hxbkConfig.getYearPeriodStartTime();//湖消年结期间开始时间
        String yearPeriodEndTime = hxbkConfig.getYearPeriodEndTime();//湖消年结期间结束时间
        LocalDateTime dateTime = LocalDateTime.now();//获取当前日期时间
        //将获取配置的年结期间时间String类型转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(yearPeriodStartTime, formatter);
        LocalDateTime endTime = LocalDateTime.parse(yearPeriodEndTime, formatter);
        //判断当前日期是否为年结期间时间段内
        boolean checkResult = dateTime.isAfter(startTime) && dateTime.isBefore(endTime);
        if(checkResult){
            //为true,说明当前时间在配置的年结期间时间段内,提示：当前时间属于年结期间不允许还款的时间，请次日再重试。
            throw new BizException(BizErrorCode.CHECK_YEAR_PERIOD_ERROR);
        }
        return true;
    }

    /**
     * 检验还款黑暗期(22:30-4:00)
     */
    private void checkRepayDarkPeriod() {
        //规定时间格式(时，分)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmm");
        String repayDarkPeriodStartTime = hxbkConfig.getRepayDarkPeriodStartTime();//湖消还款黑暗期开始时间
        String repayDarkPeriodEndTime = hxbkConfig.getRepayDarkPeriodEndTime();//湖消还款黑暗期结束时间
        LocalTime dateTime = LocalTime.now();//获取当前日期时间
        //将获取配置的还款黑暗期时间String类型转换为LocalTime
        LocalTime startTime = LocalTime.parse(repayDarkPeriodStartTime, formatter);
        LocalTime endTime = LocalTime.parse(repayDarkPeriodEndTime, formatter);
        //判断当前日期是否为还款黑暗期时间段内
        boolean checkResult = dateTime.isAfter(startTime) && dateTime.isBefore(endTime);
        if(checkResult){
            //为true,说明当前时间在配置的还款黑暗期时间段内,提示：当前时间属于还款黑暗期(22:30-4:00)不允许还款，请稍后重试。
            throw new BizException(BizErrorCode.CHECK_REPAY_DARK_PERIOD_ERROR);
        }
    }

    @Override
    public void changeOnLoan(String loanId, LoanStage stage, BigDecimal amount) {
        getCommonService().changeOnLoan(loanId, LoanStage.AFTER_LOAN, amount);
    }

    /**
     * 只连续3期代偿 触发回购
     */
    @Override
    protected boolean isTriggerRecycle(Integer totalPeriod, Integer currentPeriod, List<BankLoanReplan> plans) {
        //最后一期
        if (currentPeriod.compareTo(totalPeriod) == 0) {
            return false;
        }
        if (plans.size() < 2) {
            return false;
        }
        //累计6期 不回购
        return Objects.equals(RepayType.CLAIM, plans.get(0).getRepayType()) && Objects.equals(RepayType.CLAIM,plans.get(1).getRepayType());
    }

    @Override
    public void updatePlan(String loanId, Integer period, TrailResultVo trailResult) {
        LoanReplan repayPlan = getFinRepayService().getRepayPlan(loanId, period);
        repayPlan.setPenaltyAmt(trailResult.getOverdueFee());
        repayPlan.setBreachAmt(trailResult.getBreachFee());
        repayPlan.setTotalAmt(AmountUtil.sum(
                repayPlan.getPrincipalAmt(),
                repayPlan.getInterestAmt(),
                repayPlan.getPenaltyAmt(),
                repayPlan.getBreachAmt(),
                repayPlan.getGuaranteeAmt(),
                repayPlan.getConsultAmt()));
        getFinRepayService().updateRepayPlan(repayPlan);
    }

    @Override
    public BankRepayRecord initBankRepayRecord(TrailResultVo trailResult, String loanId, Integer period) {
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setPrincipalAmt(AmountUtil.safeNum(trailResult.getPrincipal()));
        bankRepayRecord.setInterestAmt(AmountUtil.safeNum(trailResult.getInterest()));
        bankRepayRecord.setGuaranteeAmt(AmountUtil.safeNum(trailResult.getGuaranteeFee()));
        bankRepayRecord.setPenaltyAmt(AmountUtil.safeNum(trailResult.getOverdueFee()));
        bankRepayRecord.setBreachAmt(AmountUtil.safeNum(trailResult.getBreachFee()));
        return bankRepayRecord;
    }

    //资方代还组装还款参数
    @Override
    public void populateRepayApplyVo(RepayApplyVo applyVo, String loanId) {
        // 拼装银行卡信息
        Loan loan = getCommonService().findLoanById(loanId);
        AccountBankCard bankCard = getFinRepayService().getBankCard(loan.getRepayCardId());
        String bankCode = bankCard.getBankCode();
        //湖消直连通过银行编码获取银行名称
        HXBKBankList hxbkBankList = hxbkBankRepository.findBankByBankCode(bankCode);
        if (Objects.nonNull(hxbkBankList)) {
            String isSupRepayFlg = hxbkBankList.getIsSupRepayFlg();//是否支持还款标志(默认N)：Y:是 N:否
            String bankName = hxbkBankList.getBankName();//银行名称
            //检验该银行是否支持还款
            if(Objects.equals(isSupRepayFlg,"Y")){//支持
                applyVo.setRepayBankName(bankName);//账号开户行
            }else{//不支持
                logger.info("{}不属于支持还款的银行，请检查！", bankName);
                //如果是不支持还款的银行，提示异常信息：xx银行不属于支持还款的银行，请检查！
                throw new BizException("70043", bankName + "不属于支持还款的银行，请检查！");
            }
        }
        applyVo.setRepayRelUser(bankCard.getCardName());
        applyVo.setRepayAcctNo(bankCard.getCardNo());
        applyVo.setRepayBankCode(bankCode);
        applyVo.setAgreementNo(bankCard.getAgreeNo());
    }

    @Override
    public void bankRepayApplyMq(String bankRepayRecordId) {

    }

    @Override
    public CompensatedRepaySyncRlt bankCompensatedRepaySync(CompensatedRepaySyncVo syncVo) {
        return null;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return Objects.equals(BankChannel.HXBK,channel);
    }

}
